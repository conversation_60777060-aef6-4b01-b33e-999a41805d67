# Google Analytics (GA4) Integration Guide

This document explains how Google Analytics (GA4) is integrated into the XOSportsHub application and how to use it effectively.

## 🚀 Quick Setup

### 1. Environment Configuration

Add your GA4 Measurement ID to your `.env` file:

```env
# Google Analytics Configuration
VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

Replace `G-XXXXXXXXXX` with your actual GA4 Measurement ID from Google Analytics.

### 2. How to Get Your GA4 Measurement ID

1. Go to [Google Analytics](https://analytics.google.com/)
2. Create a new GA4 property or select an existing one
3. Go to **Admin** → **Property** → **Data Streams**
4. Select your web data stream
5. Copy the **Measurement ID** (starts with `G-`)

## 📊 Features

### Automatic Tracking
- ✅ **Pageview Tracking**: Automatically tracks page views on route changes
- ✅ **Development Mode**: Skips tracking in development to avoid polluting data
- ✅ **Error Handling**: Graceful fallbacks if GA4 fails to load
- ✅ **Privacy Compliant**: IP anonymization enabled by default

### Custom Event Tracking
- 🔑 **Authentication Events**: Login, signup tracking
- 💰 **E-commerce Events**: Purchase, download tracking
- 🔍 **Search Events**: Search query tracking
- 📊 **Custom Events**: Any user interaction tracking

## 🛠️ Usage Examples

### Basic Event Tracking

```javascript
import { trackEvent } from '../utils/GoogleAnalytics';

// Track a custom event
trackEvent('button_click', {
  button_name: 'download_content',
  content_id: 'content_123',
  user_role: 'buyer'
});
```

### Authentication Tracking

```javascript
import { trackLogin, trackSignup } from '../utils/GoogleAnalytics';

// Track user login
const handleLogin = async (credentials) => {
  try {
    const result = await authService.login(credentials);
    trackLogin('email', result.user.role);
    // ... rest of login logic
  } catch (error) {
    // Handle error
  }
};

// Track user signup
const handleSignup = async (userData) => {
  try {
    const result = await authService.signup(userData);
    trackSignup('email', userData.role);
    // ... rest of signup logic
  } catch (error) {
    // Handle error
  }
};
```

### E-commerce Tracking

```javascript
import { trackPurchase, trackDownload } from '../utils/GoogleAnalytics';

// Track purchase completion
const handlePurchaseComplete = (orderData) => {
  trackPurchase({
    orderId: orderData.id,
    value: orderData.totalAmount,
    currency: 'USD',
    items: orderData.items.map(item => ({
      item_id: item.contentId,
      item_name: item.title,
      item_category: item.contentType,
      price: item.price,
      quantity: 1
    }))
  });
};

// Track content download
const handleDownload = (contentData) => {
  trackDownload({
    contentId: contentData.id,
    contentTitle: contentData.title,
    contentType: contentData.type,
    sellerId: contentData.sellerId,
    price: contentData.price
  });
};
```

### Search Tracking

```javascript
import { trackSearch } from '../utils/GoogleAnalytics';

const handleSearch = (searchTerm, resultsCount) => {
  trackSearch(searchTerm, resultsCount);
};
```

## 🔧 Available Functions

### Core Functions

| Function | Description | Parameters |
|----------|-------------|------------|
| `initializeGA()` | Initialize GA4 (called automatically) | None |
| `trackPageView(path, title)` | Track page view | `path`: string, `title`: string (optional) |
| `trackEvent(name, params)` | Track custom event | `name`: string, `params`: object |

### Convenience Functions

| Function | Description | Parameters |
|----------|-------------|------------|
| `trackLogin(method, role)` | Track user login | `method`: string, `role`: string |
| `trackSignup(method, role)` | Track user signup | `method`: string, `role`: string |
| `trackPurchase(orderData)` | Track purchase | `orderData`: object |
| `trackDownload(contentData)` | Track download | `contentData`: object |
| `trackSearch(term, count)` | Track search | `term`: string, `count`: number |

### Utility Functions

| Function | Description | Returns |
|----------|-------------|---------|
| `isGAInitialized()` | Check if GA4 is ready | boolean |
| `setUserProperties(props)` | Set user properties | void |

## 🎯 Integration Points

### Recommended Integration Locations

1. **Authentication Components**
   - Login forms: Track login attempts and successes
   - Signup forms: Track registration completions
   - OTP verification: Track verification success

2. **E-commerce Components**
   - Checkout pages: Track purchase initiation
   - Payment success: Track purchase completion
   - Download buttons: Track content downloads

3. **Search Components**
   - Search bars: Track search queries
   - Filter applications: Track filter usage

4. **Content Interaction**
   - Video plays: Track preview plays
   - PDF views: Track document opens
   - Wishlist additions: Track content saves

## 🔒 Privacy & Compliance

The integration includes privacy-friendly defaults:

- **IP Anonymization**: Enabled by default
- **Development Mode**: No tracking in development
- **Consent Ready**: Easy to integrate with consent management
- **Data Minimization**: Only essential data is tracked

### Adding Consent Management

```javascript
import { initializeGA } from '../utils/GoogleAnalytics';

// Only initialize if user has consented
if (userHasConsented) {
  initializeGA();
}
```

## 🐛 Debugging

### Development Mode

In development mode, all tracking calls are logged to the console instead of being sent to GA4:

```
🔍 GA4: Would track pageview: { path: '/dashboard', title: 'Dashboard' }
🔍 GA4: Would track event: { eventName: 'login', parameters: { method: 'email' } }
```

### Check Initialization Status

```javascript
import { isGAInitialized, GAConfig } from '../utils/GoogleAnalytics';

console.log('GA4 Initialized:', isGAInitialized());
console.log('GA4 Config:', GAConfig);
```

## 📈 Viewing Data

1. Go to [Google Analytics](https://analytics.google.com/)
2. Select your property
3. Navigate to **Reports** → **Realtime** to see live data
4. Use **Reports** → **Events** to see custom events
5. Use **Reports** → **Engagement** → **Pages and screens** for pageview data

## 🚨 Troubleshooting

### Common Issues

1. **No data in GA4**
   - Check that `VITE_GA_MEASUREMENT_ID` is set correctly
   - Verify you're not in development mode
   - Check browser console for errors

2. **Events not showing**
   - Events may take a few minutes to appear in GA4
   - Check the Realtime reports first
   - Verify event names follow GA4 conventions

3. **Development tracking**
   - Tracking is disabled in development by default
   - Check console logs for debug information

### Debug Mode

To enable debug mode in production (for testing):

```javascript
// Temporarily enable debug logging
window.gtag('config', 'G-XXXXXXXXXX', {
  debug_mode: true
});
```

## 📚 Additional Resources

- [GA4 Documentation](https://developers.google.com/analytics/devguides/collection/ga4)
- [GA4 Event Reference](https://developers.google.com/analytics/devguides/collection/ga4/reference/events)
- [GA4 Best Practices](https://support.google.com/analytics/answer/9267735)
