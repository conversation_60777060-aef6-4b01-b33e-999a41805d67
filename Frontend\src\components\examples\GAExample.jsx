/**
 * Google Analytics Usage Examples
 * 
 * Shows how to integrate GA4 tracking into your components
 */

import React from 'react';
import {
  trackEvent,
  trackLogin,
  trackSignup,
  trackPurchase,
  trackDownload,
  trackSearch
} from '../../utils/GoogleAnalytics';

const GAExample = () => {
  
  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>🚀 Google Analytics Integration Examples</h2>
      <p>Click buttons below to test GA4 tracking (check browser console):</p>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '15px', marginTop: '20px' }}>
        
        {/* Login Tracking */}
        <button 
          onClick={() => {
            trackLogin('email', 'buyer');
            console.log('✅ Login tracked!');
          }}
          style={{ padding: '12px', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '6px', cursor: 'pointer' }}
        >
          🔑 Track Login Event
        </button>

        {/* Signup Tracking */}
        <button 
          onClick={() => {
            trackSignup('email', 'seller');
            console.log('✅ Signup tracked!');
          }}
          style={{ padding: '12px', backgroundColor: '#28a745', color: 'white', border: 'none', borderRadius: '6px', cursor: 'pointer' }}
        >
          ✍️ Track Signup Event
        </button>

        {/* Purchase Tracking */}
        <button 
          onClick={() => {
            trackPurchase({
              orderId: 'order_123',
              value: 29.99,
              currency: 'USD',
              items: [{ item_id: 'content_456', item_name: 'Basketball Guide', price: 29.99 }]
            });
            console.log('✅ Purchase tracked!');
          }}
          style={{ padding: '12px', backgroundColor: '#ffc107', color: 'black', border: 'none', borderRadius: '6px', cursor: 'pointer' }}
        >
          💰 Track Purchase Event
        </button>

        {/* Download Tracking */}
        <button 
          onClick={() => {
            trackDownload({
              contentId: 'content_789',
              contentTitle: 'Football Training Video',
              contentType: 'Video',
              sellerId: 'seller_123',
              price: 19.99
            });
            console.log('✅ Download tracked!');
          }}
          style={{ padding: '12px', backgroundColor: '#17a2b8', color: 'white', border: 'none', borderRadius: '6px', cursor: 'pointer' }}
        >
          📥 Track Download Event
        </button>

        {/* Search Tracking */}
        <button 
          onClick={() => {
            trackSearch('basketball strategies', 15);
            console.log('✅ Search tracked!');
          }}
          style={{ padding: '12px', backgroundColor: '#6f42c1', color: 'white', border: 'none', borderRadius: '6px', cursor: 'pointer' }}
        >
          🔍 Track Search Event
        </button>

        {/* Custom Event Tracking */}
        <button 
          onClick={() => {
            trackEvent('button_click', {
              button_name: 'newsletter_signup',
              page_path: window.location.pathname,
              timestamp: new Date().toISOString()
            });
            console.log('✅ Custom event tracked!');
          }}
          style={{ padding: '12px', backgroundColor: '#fd7e14', color: 'white', border: 'none', borderRadius: '6px', cursor: 'pointer' }}
        >
          📧 Track Custom Event
        </button>

      </div>

      {/* Integration Examples */}
      <div style={{ marginTop: '40px', padding: '20px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
        <h3>💡 How to integrate into your components:</h3>
        
        <h4>1. Login Component Example:</h4>
        <pre style={{ backgroundColor: '#e9ecef', padding: '15px', borderRadius: '4px', fontSize: '14px', overflow: 'auto' }}>
{`// In your login success handler
import { trackLogin } from '../utils/GoogleAnalytics';

const handleLoginSuccess = (user) => {
  trackLogin('email', user.role);
  // ... rest of your login logic
};`}
        </pre>

        <h4>2. Purchase Component Example:</h4>
        <pre style={{ backgroundColor: '#e9ecef', padding: '15px', borderRadius: '4px', fontSize: '14px', overflow: 'auto' }}>
{`// In your checkout success handler
import { trackPurchase } from '../utils/GoogleAnalytics';

const handlePurchaseComplete = (orderData) => {
  trackPurchase({
    orderId: orderData.id,
    value: orderData.totalAmount,
    currency: 'USD',
    items: orderData.items
  });
  // ... rest of your success logic
};`}
        </pre>

        <h4>3. Download Button Example:</h4>
        <pre style={{ backgroundColor: '#e9ecef', padding: '15px', borderRadius: '4px', fontSize: '14px', overflow: 'auto' }}>
{`// In your download button click handler
import { trackDownload } from '../utils/GoogleAnalytics';

const handleDownloadClick = (content) => {
  trackDownload({
    contentId: content.id,
    contentTitle: content.title,
    contentType: content.type,
    price: content.price
  });
  // ... start download
};`}
        </pre>

        <h4>4. Search Component Example:</h4>
        <pre style={{ backgroundColor: '#e9ecef', padding: '15px', borderRadius: '4px', fontSize: '14px', overflow: 'auto' }}>
{`// In your search submit handler
import { trackSearch } from '../utils/GoogleAnalytics';

const handleSearch = (searchTerm, results) => {
  trackSearch(searchTerm, results.length);
  // ... display results
};`}
        </pre>

        <h4>5. Custom Event Example:</h4>
        <pre style={{ backgroundColor: '#e9ecef', padding: '15px', borderRadius: '4px', fontSize: '14px', overflow: 'auto' }}>
{`// Track any custom interaction
import { trackEvent } from '../utils/GoogleAnalytics';

const handleCustomAction = () => {
  trackEvent('video_play', {
    video_id: 'video_123',
    video_title: 'Training Preview',
    content_type: 'preview'
  });
  // ... your action logic
};`}
        </pre>

        <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#d4edda', borderRadius: '4px', border: '1px solid #c3e6cb' }}>
          <strong>✅ Setup Complete!</strong>
          <p style={{ margin: '10px 0 0 0' }}>
            Your GA4 is ready to use. Just add your <code>VITE_GA_MEASUREMENT_ID</code> to your .env file and start tracking!
          </p>
        </div>
      </div>
    </div>
  );
};

export default GAExample;
