/**
 * Google Analytics Usage Examples
 * 
 * This component demonstrates how to use the Google Analytics utility
 * for tracking custom events in your XOSportsHub application.
 * 
 * You can integrate these tracking calls into your existing components
 * where user interactions occur.
 */

import React from 'react';
import { 
  trackEvent, 
  trackLogin, 
  trackSignup, 
  trackPurchase, 
  trackDownload, 
  trackSearch,
  setUserProperties 
} from '../../utils/GoogleAnalytics';

const GoogleAnalyticsExample = () => {
  
  // Example: Track login button click
  const handleLoginClick = () => {
    // Your existing login logic here...
    console.log('User attempting to login...');
    
    // Track the login attempt
    trackLogin('email', 'buyer'); // method: 'email', userRole: 'buyer'
    
    // Or use the generic trackEvent for more custom data
    trackEvent('login_attempt', {
      login_method: 'email',
      page_location: window.location.pathname,
      user_type: 'returning_user'
    });
  };

  // Example: Track signup completion
  const handleSignupSuccess = (userData) => {
    // Your existing signup success logic here...
    console.log('User signed up successfully:', userData);
    
    // Track the signup
    trackSignup('email', userData.role);
    
    // Set user properties for future tracking
    setUserProperties({
      user_role: userData.role,
      signup_date: new Date().toISOString(),
      user_id: userData.id
    });
  };

  // Example: Track content purchase
  const handlePurchaseComplete = (orderData) => {
    // Your existing purchase completion logic here...
    console.log('Purchase completed:', orderData);
    
    // Track the purchase
    trackPurchase({
      orderId: orderData.id,
      value: orderData.totalAmount,
      currency: 'USD',
      items: orderData.items.map(item => ({
        item_id: item.contentId,
        item_name: item.title,
        item_category: item.contentType,
        price: item.price,
        quantity: 1
      }))
    });
  };

  // Example: Track content download
  const handleDownloadClick = (contentData) => {
    // Your existing download logic here...
    console.log('User downloading content:', contentData);
    
    // Track the download
    trackDownload({
      contentId: contentData.id,
      contentTitle: contentData.title,
      contentType: contentData.type,
      sellerId: contentData.sellerId,
      price: contentData.price
    });
  };

  // Example: Track search
  const handleSearch = (searchTerm, resultsCount) => {
    // Your existing search logic here...
    console.log('User searched for:', searchTerm);
    
    // Track the search
    trackSearch(searchTerm, resultsCount);
  };

  // Example: Track custom button clicks
  const handleCustomButtonClick = (buttonName) => {
    // Track any custom interaction
    trackEvent('button_click', {
      button_name: buttonName,
      page_path: window.location.pathname,
      timestamp: new Date().toISOString()
    });
  };

  // Example: Track video play (for content previews)
  const handleVideoPlay = (videoData) => {
    trackEvent('video_play', {
      video_id: videoData.id,
      video_title: videoData.title,
      video_duration: videoData.duration,
      content_type: 'preview'
    });
  };

  // Example: Track form submissions
  const handleFormSubmit = (formType, formData) => {
    trackEvent('form_submit', {
      form_type: formType,
      form_fields: Object.keys(formData).length,
      success: true
    });
  };

  return (
    <div className="analytics-example-container" style={{ padding: '20px', maxWidth: '800px' }}>
      <h2>Google Analytics Integration Examples</h2>
      <p>Click the buttons below to see how Google Analytics events are tracked:</p>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '10px', marginTop: '20px' }}>
        
        {/* Login Example */}
        <button 
          onClick={handleLoginClick}
          style={{ padding: '10px', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '4px' }}
        >
          🔑 Track Login Click
        </button>

        {/* Signup Example */}
        <button 
          onClick={() => handleSignupSuccess({ id: '123', role: 'buyer', email: '<EMAIL>' })}
          style={{ padding: '10px', backgroundColor: '#28a745', color: 'white', border: 'none', borderRadius: '4px' }}
        >
          ✅ Track Signup Success
        </button>

        {/* Purchase Example */}
        <button 
          onClick={() => handlePurchaseComplete({
            id: 'order_123',
            totalAmount: 29.99,
            items: [
              { contentId: 'content_456', title: 'Basketball Strategy Guide', contentType: 'PDF', price: 29.99 }
            ]
          })}
          style={{ padding: '10px', backgroundColor: '#ffc107', color: 'black', border: 'none', borderRadius: '4px' }}
        >
          💰 Track Purchase
        </button>

        {/* Download Example */}
        <button 
          onClick={() => handleDownloadClick({
            id: 'content_789',
            title: 'Football Training Video',
            type: 'Video',
            sellerId: 'seller_123',
            price: 19.99
          })}
          style={{ padding: '10px', backgroundColor: '#17a2b8', color: 'white', border: 'none', borderRadius: '4px' }}
        >
          📥 Track Download
        </button>

        {/* Search Example */}
        <button 
          onClick={() => handleSearch('basketball strategies', 15)}
          style={{ padding: '10px', backgroundColor: '#6f42c1', color: 'white', border: 'none', borderRadius: '4px' }}
        >
          🔍 Track Search
        </button>

        {/* Custom Event Example */}
        <button 
          onClick={() => handleCustomButtonClick('newsletter_signup')}
          style={{ padding: '10px', backgroundColor: '#fd7e14', color: 'white', border: 'none', borderRadius: '4px' }}
        >
          📧 Track Custom Event
        </button>

        {/* Video Play Example */}
        <button 
          onClick={() => handleVideoPlay({
            id: 'video_123',
            title: 'Soccer Training Preview',
            duration: 120
          })}
          style={{ padding: '10px', backgroundColor: '#e83e8c', color: 'white', border: 'none', borderRadius: '4px' }}
        >
          ▶️ Track Video Play
        </button>

        {/* Form Submit Example */}
        <button 
          onClick={() => handleFormSubmit('contact_form', { name: 'John', email: '<EMAIL>', message: 'Hello' })}
          style={{ padding: '10px', backgroundColor: '#20c997', color: 'white', border: 'none', borderRadius: '4px' }}
        >
          📝 Track Form Submit
        </button>
      </div>

      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
        <h3>Integration Tips:</h3>
        <ul>
          <li><strong>Login/Signup:</strong> Add tracking to your authentication components</li>
          <li><strong>Purchases:</strong> Add tracking to your checkout success pages</li>
          <li><strong>Downloads:</strong> Add tracking to your download buttons/links</li>
          <li><strong>Search:</strong> Add tracking to your search functionality</li>
          <li><strong>Custom Events:</strong> Track any important user interactions</li>
        </ul>
        
        <h4>Example Integration in Existing Components:</h4>
        <pre style={{ backgroundColor: '#e9ecef', padding: '10px', borderRadius: '4px', fontSize: '12px' }}>
{`// In your login component:
import { trackLogin } from '../utils/GoogleAnalytics';

const handleLogin = async (credentials) => {
  try {
    const result = await authService.login(credentials);
    trackLogin('email', result.user.role);
    // ... rest of your login logic
  } catch (error) {
    // Handle error
  }
};`}
        </pre>
      </div>
    </div>
  );
};

export default GoogleAnalyticsExample;
