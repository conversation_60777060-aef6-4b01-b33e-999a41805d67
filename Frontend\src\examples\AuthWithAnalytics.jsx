/**
 * Example: Authentication Component with Google Analytics Integration
 * 
 * This is an example showing how to integrate Google Analytics tracking
 * into your existing authentication components. You can apply these
 * patterns to your actual Auth.jsx and Signup.jsx components.
 * 
 * Key tracking points:
 * - Login attempts and successes
 * - Google sign-in usage
 * - Form validation errors
 * - User role tracking
 */

import React, { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";

// Import Google Analytics functions
import { 
  trackEvent, 
  trackLogin, 
  setUserProperties 
} from "../utils/GoogleAnalytics";

// Your existing imports...
import { login, reset, googleSignIn } from "../redux/slices/authSlice";
import toast from "../utils/toast";
import GoogleSignInButton from "../components/common/GoogleSignInButton";
import { MdMailOutline } from "react-icons/md";
import firebaseService from "../services/firebaseService";
import { getSellerRedirectPath } from "../utils/sellerUtils";

const AuthWithAnalytics = () => {
  const [formData, setFormData] = useState({ email: "" });
  const [errors, setErrors] = useState({});
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state) => state.auth);

  const validateForm = () => {
    const newErrors = {};
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email is invalid";
    }
    return newErrors;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Track login attempt
    trackEvent('login_attempt', {
      method: 'email',
      page_location: window.location.pathname
    });

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      
      // Track validation errors
      trackEvent('form_validation_error', {
        form_type: 'login',
        error_fields: Object.keys(validationErrors),
        error_count: Object.keys(validationErrors).length
      });
      
      return;
    }

    dispatch(reset());

    try {
      // Dispatch login action with email
      const result = await dispatch(login({ email: formData.email })).unwrap();

      // Track successful login initiation
      trackEvent('login_otp_sent', {
        method: 'email',
        is_completing_registration: result.isCompletingRegistration || false,
        user_id: result.userId
      });

      // Show appropriate success message
      if (result.isCompletingRegistration) {
        toast.otp.success(result.message);
      } else {
        toast.otp.success(result.message);
      }

      // Navigate to OTP verification page
      navigate("/otp-verification", {
        state: {
          userId: result.userId,
          email: formData.email,
          cooldownSeconds: result.cooldownSeconds || 60,
          isLogin: true,
          isCompletingRegistration: result.isCompletingRegistration || false,
          developmentOtp: result.developmentOtp,
        },
      });

    } catch (error) {
      // Track login errors
      trackEvent('login_error', {
        method: 'email',
        error_type: error?.code || 'unknown',
        error_message: error?.message || 'Login failed'
      });

      let errorMessage = "Login failed. Please try again.";
      if (typeof error === 'string') {
        errorMessage = error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast.auth.loginError(errorMessage);
      setErrors({ general: errorMessage });
    }
  };

  const handleGoogleSignIn = async () => {
    // Track Google sign-in attempt
    trackEvent('login_attempt', {
      method: 'google',
      page_location: window.location.pathname
    });

    try {
      const result = await firebaseService.signInWithGoogle();
      
      if (!result.success) {
        throw new Error(result.error);
      }

      // Try to sign in with existing account
      try {
        const response = await dispatch(googleSignIn(result.idToken)).unwrap();

        // Track successful Google login
        trackLogin('google', response.user.role);

        // Set user properties for future tracking
        setUserProperties({
          user_role: response.user.role,
          user_id: response.user.id,
          login_method: 'google',
          onboarding_completed: response.user.onboardingCompleted || false
        });

        // Track successful login
        trackEvent('login_success', {
          method: 'google',
          user_role: response.user.role,
          user_id: response.user.id,
          redirect_path: response.user.role === 'buyer' ? '/content' : 
                        response.user.role === 'seller' ? getSellerRedirectPath(response.user) :
                        response.user.role === 'admin' ? '/admin/dashboard' : '/'
        });

        toast.auth.loginSuccess();

        // Navigate based on user role
        if (response.user.role === "buyer") {
          navigate("/content");
        } else if (response.user.role === "seller") {
          const redirectPath = getSellerRedirectPath(response.user);
          navigate(redirectPath);
        } else if (response.user.role === "admin") {
          navigate("/admin/dashboard");
        } else {
          navigate("/");
        }

      } catch (signInError) {
        // Track Google sign-in error
        trackEvent('login_error', {
          method: 'google',
          error_type: signInError?.code || 'signin_failed',
          error_message: signInError?.message || 'Google sign-in failed'
        });

        // Handle case where Google account doesn't exist in our system
        if (signInError?.message?.includes('not found') || signInError?.code === 'USER_NOT_FOUND') {
          toast.auth.userNotFound();
          navigate("/signup", { 
            state: { 
              googleData: result,
              email: result.email 
            } 
          });
        } else {
          toast.auth.loginError(signInError.message || "Google sign-in failed");
        }
      }

    } catch (error) {
      // Track Google authentication error
      trackEvent('google_auth_error', {
        error_type: error?.code || 'unknown',
        error_message: error?.message || 'Google authentication failed'
      });

      console.error("Google sign-in error:", error);
      toast.auth.loginError("Google sign-in failed. Please try again.");
    }
  };

  return (
    <div className="auth">
      <div className="auth__container">
        <div className="auth__header">
          <h1 className="auth__title">Welcome Back!</h1>
          <p className="auth__subtitle">
            Sign in to access your XO Sports Hub account
          </p>
        </div>

        <form onSubmit={handleSubmit} className="auth__form">
          {errors.general && (
            <div className="auth__error-banner">
              {errors.general}
            </div>
          )}

          <div className="auth__input-container">
            <div className="auth__input-icon">
              <MdMailOutline />
            </div>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter your email address"
              className={`auth__input ${errors.email ? "auth__input--error" : ""}`}
              required
            />
            {errors.email && (
              <p className="auth__error">{errors.email}</p>
            )}
          </div>

          <button 
            type="submit" 
            className="auth__button" 
            disabled={isLoading}
          >
            {isLoading ? "Sending OTP..." : "Continue with Email"}
          </button>

          <div className="auth__divider">
            <span>or</span>
          </div>

          <GoogleSignInButton
            onClick={handleGoogleSignIn}
            isLoading={isLoading}
            text="Continue with Google"
            variant="primary"
          />

          <p className="auth__signup-link">
            Don't have an account?{" "}
            <Link 
              to="/signup"
              onClick={() => {
                // Track navigation to signup
                trackEvent('navigation_click', {
                  from_page: 'login',
                  to_page: 'signup',
                  link_text: 'Create Account'
                });
              }}
              className="auth__link"
            >
              Create Account
            </Link>
          </p>
        </form>
      </div>
    </div>
  );
};

export default AuthWithAnalytics;
