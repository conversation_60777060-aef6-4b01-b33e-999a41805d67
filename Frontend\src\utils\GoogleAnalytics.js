/**
 * Google Analytics (GA4) Integration Utility
 * 
 * This utility provides a clean interface for Google Analytics tracking
 * with automatic pageview tracking and custom event tracking capabilities.
 * 
 * Features:
 * - Environment-based configuration
 * - Development mode detection
 * - Automatic pageview tracking
 * - Custom event tracking
 * - Error handling and fallbacks
 */

// Get GA4 Measurement ID from environment variables
const GA_MEASUREMENT_ID = import.meta.env.VITE_GA_MEASUREMENT_ID;
const IS_DEVELOPMENT = import.meta.env.DEV;
const IS_PRODUCTION = import.meta.env.PROD;

/**
 * Initialize Google Analytics
 * This should be called once when the app starts
 */
export const initializeGA = () => {
  // Skip initialization in development mode or if no measurement ID is provided
  if (IS_DEVELOPMENT || !GA_MEASUREMENT_ID) {
    if (IS_DEVELOPMENT) {
      console.log('🔍 GA4: Skipping initialization in development mode');
    }
    if (!GA_MEASUREMENT_ID) {
      console.warn('⚠️ GA4: No measurement ID found. Please set VITE_GA_MEASUREMENT_ID in your .env file');
    }
    return;
  }

  try {
    // Load Google Analytics script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`;
    document.head.appendChild(script);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      window.dataLayer.push(arguments);
    }
    window.gtag = gtag;

    // Configure GA4
    gtag('js', new Date());
    gtag('config', GA_MEASUREMENT_ID, {
      // Enhanced measurement settings
      send_page_view: true,
      allow_google_signals: true,
      allow_ad_personalization_signals: true,
      // Privacy settings
      anonymize_ip: true,
      // Custom settings for SPA
      page_title: document.title,
      page_location: window.location.href,
    });

    console.log('✅ GA4: Successfully initialized with ID:', GA_MEASUREMENT_ID);
  } catch (error) {
    console.error('❌ GA4: Failed to initialize:', error);
  }
};

/**
 * Track a pageview
 * Call this when the route changes in your SPA
 * 
 * @param {string} path - The page path (e.g., '/dashboard', '/content/123')
 * @param {string} title - The page title (optional)
 */
export const trackPageView = (path, title = null) => {
  if (IS_DEVELOPMENT || !GA_MEASUREMENT_ID || typeof window.gtag !== 'function') {
    if (IS_DEVELOPMENT) {
      console.log('🔍 GA4: Would track pageview:', { path, title });
    }
    return;
  }

  try {
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_path: path,
      page_title: title || document.title,
      page_location: `${window.location.origin}${path}`,
    });

    console.log('📊 GA4: Pageview tracked:', path);
  } catch (error) {
    console.error('❌ GA4: Failed to track pageview:', error);
  }
};

/**
 * Track a custom event
 * Use this for user interactions, conversions, etc.
 * 
 * @param {string} eventName - The event name (e.g., 'login', 'purchase', 'download')
 * @param {Object} parameters - Event parameters (optional)
 */
export const trackEvent = (eventName, parameters = {}) => {
  if (IS_DEVELOPMENT || !GA_MEASUREMENT_ID || typeof window.gtag !== 'function') {
    if (IS_DEVELOPMENT) {
      console.log('🔍 GA4: Would track event:', { eventName, parameters });
    }
    return;
  }

  try {
    window.gtag('event', eventName, {
      // Add timestamp
      event_timestamp: Date.now(),
      // Merge custom parameters
      ...parameters,
    });

    console.log('📊 GA4: Event tracked:', eventName, parameters);
  } catch (error) {
    console.error('❌ GA4: Failed to track event:', error);
  }
};

/**
 * Track user login
 * Convenience method for login events
 * 
 * @param {string} method - Login method (e.g., 'email', 'google', 'facebook')
 * @param {string} userRole - User role (e.g., 'buyer', 'seller', 'admin')
 */
export const trackLogin = (method = 'email', userRole = null) => {
  trackEvent('login', {
    method,
    user_role: userRole,
  });
};

/**
 * Track user signup
 * Convenience method for signup events
 * 
 * @param {string} method - Signup method (e.g., 'email', 'google', 'facebook')
 * @param {string} userRole - User role (e.g., 'buyer', 'seller')
 */
export const trackSignup = (method = 'email', userRole = null) => {
  trackEvent('sign_up', {
    method,
    user_role: userRole,
  });
};

/**
 * Track purchase/order completion
 * Convenience method for e-commerce tracking
 * 
 * @param {Object} orderData - Order information
 */
export const trackPurchase = (orderData = {}) => {
  const {
    orderId,
    value,
    currency = 'USD',
    items = [],
    ...otherData
  } = orderData;

  trackEvent('purchase', {
    transaction_id: orderId,
    value,
    currency,
    items,
    ...otherData,
  });
};

/**
 * Track content download
 * Convenience method for content download tracking
 * 
 * @param {Object} contentData - Content information
 */
export const trackDownload = (contentData = {}) => {
  const {
    contentId,
    contentTitle,
    contentType,
    sellerId,
    price,
    ...otherData
  } = contentData;

  trackEvent('download', {
    content_id: contentId,
    content_title: contentTitle,
    content_type: contentType,
    seller_id: sellerId,
    value: price,
    ...otherData,
  });
};

/**
 * Track search
 * Convenience method for search tracking
 * 
 * @param {string} searchTerm - The search query
 * @param {number} resultsCount - Number of results (optional)
 */
export const trackSearch = (searchTerm, resultsCount = null) => {
  trackEvent('search', {
    search_term: searchTerm,
    results_count: resultsCount,
  });
};

/**
 * Set user properties
 * Use this to set user-level custom dimensions
 * 
 * @param {Object} properties - User properties
 */
export const setUserProperties = (properties = {}) => {
  if (IS_DEVELOPMENT || !GA_MEASUREMENT_ID || typeof window.gtag !== 'function') {
    if (IS_DEVELOPMENT) {
      console.log('🔍 GA4: Would set user properties:', properties);
    }
    return;
  }

  try {
    window.gtag('config', GA_MEASUREMENT_ID, {
      custom_map: properties,
    });

    console.log('📊 GA4: User properties set:', properties);
  } catch (error) {
    console.error('❌ GA4: Failed to set user properties:', error);
  }
};

/**
 * Check if GA4 is properly initialized
 * 
 * @returns {boolean} True if GA4 is ready to use
 */
export const isGAInitialized = () => {
  return !IS_DEVELOPMENT && !!GA_MEASUREMENT_ID && typeof window.gtag === 'function';
};

// Export configuration for debugging
export const GAConfig = {
  measurementId: GA_MEASUREMENT_ID,
  isDevelopment: IS_DEVELOPMENT,
  isProduction: IS_PRODUCTION,
  isInitialized: isGAInitialized,
};

// Default export with all functions
export default {
  initializeGA,
  trackPageView,
  trackEvent,
  trackLogin,
  trackSignup,
  trackPurchase,
  trackDownload,
  trackSearch,
  setUserProperties,
  isGAInitialized,
  GAConfig,
};
