/**
 * Google Analytics Utility Tests
 * 
 * These tests verify that the Google Analytics utility functions
 * work correctly in different environments and scenarios.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  initializeGA,
  trackPageView,
  trackEvent,
  trackLogin,
  trackSignup,
  trackPurchase,
  trackDownload,
  trackSearch,
  isGAInitialized,
  GAConfig
} from '../GoogleAnalytics';

// Mock environment variables
const mockEnv = {
  VITE_GA_MEASUREMENT_ID: 'G-TEST123456',
  DEV: false,
  PROD: true
};

// Mock import.meta.env
vi.mock('import.meta.env', () => mockEnv);

// Mock DOM methods
Object.defineProperty(window, 'location', {
  value: {
    href: 'https://example.com/test',
    origin: 'https://example.com',
    pathname: '/test'
  },
  writable: true
});

Object.defineProperty(document, 'title', {
  value: 'Test Page',
  writable: true
});

describe('GoogleAnalytics Utility', () => {
  beforeEach(() => {
    // Reset DOM
    document.head.innerHTML = '';
    delete window.gtag;
    delete window.dataLayer;
    
    // Reset console methods
    vi.clearAllMocks();
    console.log = vi.fn();
    console.warn = vi.fn();
    console.error = vi.fn();
  });

  describe('initializeGA', () => {
    it('should initialize GA4 in production with valid measurement ID', () => {
      mockEnv.DEV = false;
      mockEnv.PROD = true;
      mockEnv.VITE_GA_MEASUREMENT_ID = 'G-TEST123456';

      initializeGA();

      // Check if script was added
      const scripts = document.head.querySelectorAll('script');
      expect(scripts.length).toBe(1);
      expect(scripts[0].src).toContain('googletagmanager.com/gtag/js?id=G-TEST123456');
      
      // Check if gtag was initialized
      expect(window.dataLayer).toBeDefined();
      expect(window.gtag).toBeDefined();
    });

    it('should skip initialization in development mode', () => {
      mockEnv.DEV = true;
      mockEnv.PROD = false;

      initializeGA();

      expect(document.head.querySelectorAll('script').length).toBe(0);
      expect(console.log).toHaveBeenCalledWith('🔍 GA4: Skipping initialization in development mode');
    });

    it('should warn when no measurement ID is provided', () => {
      mockEnv.DEV = false;
      mockEnv.VITE_GA_MEASUREMENT_ID = undefined;

      initializeGA();

      expect(console.warn).toHaveBeenCalledWith('⚠️ GA4: No measurement ID found. Please set VITE_GA_MEASUREMENT_ID in your .env file');
    });
  });

  describe('trackPageView', () => {
    beforeEach(() => {
      mockEnv.DEV = false;
      mockEnv.VITE_GA_MEASUREMENT_ID = 'G-TEST123456';
      window.gtag = vi.fn();
    });

    it('should track pageview with path and title', () => {
      trackPageView('/dashboard', 'Dashboard');

      expect(window.gtag).toHaveBeenCalledWith('config', 'G-TEST123456', {
        page_path: '/dashboard',
        page_title: 'Dashboard',
        page_location: 'https://example.com/dashboard'
      });
    });

    it('should use document title when no title provided', () => {
      trackPageView('/test');

      expect(window.gtag).toHaveBeenCalledWith('config', 'G-TEST123456', {
        page_path: '/test',
        page_title: 'Test Page',
        page_location: 'https://example.com/test'
      });
    });

    it('should log in development mode instead of tracking', () => {
      mockEnv.DEV = true;
      delete window.gtag;

      trackPageView('/test', 'Test');

      expect(console.log).toHaveBeenCalledWith('🔍 GA4: Would track pageview:', { path: '/test', title: 'Test' });
    });
  });

  describe('trackEvent', () => {
    beforeEach(() => {
      mockEnv.DEV = false;
      mockEnv.VITE_GA_MEASUREMENT_ID = 'G-TEST123456';
      window.gtag = vi.fn();
    });

    it('should track custom event with parameters', () => {
      const eventParams = { button_name: 'login', user_role: 'buyer' };
      
      trackEvent('button_click', eventParams);

      expect(window.gtag).toHaveBeenCalledWith('event', 'button_click', {
        event_timestamp: expect.any(Number),
        ...eventParams
      });
    });

    it('should track event without parameters', () => {
      trackEvent('page_load');

      expect(window.gtag).toHaveBeenCalledWith('event', 'page_load', {
        event_timestamp: expect.any(Number)
      });
    });
  });

  describe('convenience functions', () => {
    beforeEach(() => {
      mockEnv.DEV = false;
      mockEnv.VITE_GA_MEASUREMENT_ID = 'G-TEST123456';
      window.gtag = vi.fn();
    });

    it('should track login with method and role', () => {
      trackLogin('email', 'buyer');

      expect(window.gtag).toHaveBeenCalledWith('event', 'login', {
        event_timestamp: expect.any(Number),
        method: 'email',
        user_role: 'buyer'
      });
    });

    it('should track signup with method and role', () => {
      trackSignup('google', 'seller');

      expect(window.gtag).toHaveBeenCalledWith('event', 'sign_up', {
        event_timestamp: expect.any(Number),
        method: 'google',
        user_role: 'seller'
      });
    });

    it('should track purchase with order data', () => {
      const orderData = {
        orderId: 'order_123',
        value: 29.99,
        currency: 'USD',
        items: [{ item_id: 'content_456', item_name: 'Test Content' }]
      };

      trackPurchase(orderData);

      expect(window.gtag).toHaveBeenCalledWith('event', 'purchase', {
        event_timestamp: expect.any(Number),
        transaction_id: 'order_123',
        value: 29.99,
        currency: 'USD',
        items: [{ item_id: 'content_456', item_name: 'Test Content' }]
      });
    });

    it('should track download with content data', () => {
      const contentData = {
        contentId: 'content_789',
        contentTitle: 'Test Video',
        contentType: 'Video',
        sellerId: 'seller_123',
        price: 19.99
      };

      trackDownload(contentData);

      expect(window.gtag).toHaveBeenCalledWith('event', 'download', {
        event_timestamp: expect.any(Number),
        content_id: 'content_789',
        content_title: 'Test Video',
        content_type: 'Video',
        seller_id: 'seller_123',
        value: 19.99
      });
    });

    it('should track search with term and results count', () => {
      trackSearch('basketball', 15);

      expect(window.gtag).toHaveBeenCalledWith('event', 'search', {
        event_timestamp: expect.any(Number),
        search_term: 'basketball',
        results_count: 15
      });
    });
  });

  describe('isGAInitialized', () => {
    it('should return true when GA is properly initialized', () => {
      mockEnv.DEV = false;
      mockEnv.VITE_GA_MEASUREMENT_ID = 'G-TEST123456';
      window.gtag = vi.fn();

      expect(isGAInitialized()).toBe(true);
    });

    it('should return false in development mode', () => {
      mockEnv.DEV = true;
      mockEnv.VITE_GA_MEASUREMENT_ID = 'G-TEST123456';
      window.gtag = vi.fn();

      expect(isGAInitialized()).toBe(false);
    });

    it('should return false when no measurement ID', () => {
      mockEnv.DEV = false;
      mockEnv.VITE_GA_MEASUREMENT_ID = undefined;
      window.gtag = vi.fn();

      expect(isGAInitialized()).toBe(false);
    });

    it('should return false when gtag is not available', () => {
      mockEnv.DEV = false;
      mockEnv.VITE_GA_MEASUREMENT_ID = 'G-TEST123456';
      delete window.gtag;

      expect(isGAInitialized()).toBe(false);
    });
  });

  describe('GAConfig', () => {
    it('should export correct configuration', () => {
      expect(GAConfig).toEqual({
        measurementId: 'G-TEST123456',
        isDevelopment: false,
        isProduction: true,
        isInitialized: expect.any(Function)
      });
    });
  });
});
